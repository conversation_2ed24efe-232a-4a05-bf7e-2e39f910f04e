import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../widgets/app_drawer.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../models/weather_model.dart';
import '../models/weather_preference_model.dart';
import '../services/weather_service.dart';
import '../utils/app_utils.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final _databaseHelper = DatabaseHelper();
  final _weatherService = WeatherService();
  final _cityController = TextEditingController();

  UserModel? _currentUser;
  WeatherPreferenceModel? _weatherPreference;
  WeatherModel? _currentWeather;
  WeatherForecastModel? _weatherForecast;

  bool _isLoading = true;
  bool _isLoadingWeather = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);

      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
        _weatherPreference = await _databaseHelper.getWeatherPreferenceByUserId(userId);

        if (_weatherPreference != null) {
          _cityController.text = _weatherPreference!.city;
          await _loadWeatherData();
        } else {
          // Set default city if no preference exists
          _cityController.text = AppConstants.defaultCity;
          await _loadWeatherDataForCity(AppConstants.defaultCity);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load user data';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadWeatherData() async {
    if (_weatherPreference == null) return;

    await _loadWeatherDataForCity(_weatherPreference!.city);
  }

  Future<void> _loadWeatherDataForCity(String cityName) async {
    setState(() {
      _isLoadingWeather = true;
      _errorMessage = null;
    });

    try {
      final units = _weatherPreference?.temperatureUnit ?? AppConstants.defaultTemperatureUnit;

      final weather = await _weatherService.getCurrentWeatherSafe(
        cityName: cityName,
        units: units,
      );

      final forecast = await _weatherService.getWeatherForecast(
        cityName: cityName,
        units: units,
      );

      if (mounted) {
        setState(() {
          _currentWeather = weather;
          _weatherForecast = forecast;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceAll('Exception: ', '');
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingWeather = false;
        });
      }
    }
  }

  Future<void> _searchCity() async {
    final city = _cityController.text.trim();
    if (city.isEmpty) {
      AppUtils.showSnackBar(
        context,
        'Please enter a city name',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoadingWeather = true;
      _errorMessage = null;
    });

    try {
      final units = _weatherPreference?.temperatureUnit ?? AppConstants.defaultTemperatureUnit;

      final weather = await _weatherService.getCurrentWeatherSafe(
        cityName: city,
        units: units,
      );

      if (weather != null) {
        // Update weather preference if user is logged in
        if (_weatherPreference != null) {
          final updatedPreference = _weatherPreference!.copyWith(
            city: weather.cityName,
            country: weather.country,
            updatedAt: DateTime.now(),
          );

          await _databaseHelper.updateWeatherPreference(updatedPreference);
          _weatherPreference = updatedPreference;
        }

        // Load forecast for the new city
        final forecast = await _weatherService.getWeatherForecast(
          cityName: weather.cityName,
          units: units,
        );

        if (mounted) {
          setState(() {
            _currentWeather = weather;
            _weatherForecast = forecast;
          });

          AppUtils.showSnackBar(
            context,
            'Weather data loaded for ${weather.cityName}',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceAll('Exception: ', '');
        });

        AppUtils.showSnackBar(
          context,
          _errorMessage!,
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingWeather = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Weather'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadWeatherData,
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      drawer: _currentUser != null
          ? AppDrawer(
              currentRoute: AppConstants.mainRoute,
              userName: _currentUser!.fullName,
              userEmail: _currentUser!.email,
              userAvatar: _currentUser!.profileImage,
            )
          : null,
      body: RefreshIndicator(
        onRefresh: _loadWeatherData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            children: [
              if (_errorMessage != null) _buildErrorCard(),
              if (_currentWeather != null) ...[
                _buildCurrentWeatherCard(),
                const SizedBox(height: AppTheme.spacingMedium),
                _buildWeatherDetailsCard(),
                const SizedBox(height: AppTheme.spacingMedium),
                if (_weatherForecast != null) _buildForecastCard(),
              ] else if (!_isLoadingWeather) _buildNoDataCard(),
              if (_isLoadingWeather) _buildLoadingCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorCard() {
    return Card(
      color: AppTheme.errorColor.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          children: [
            Row(
              children: [
                const Icon(Icons.error, color: AppTheme.errorColor),
                const SizedBox(width: AppTheme.spacingSmall),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: AppTheme.errorColor),
                  ),
                ),
              ],
            ),

            // Show specific help based on error type
            if (_errorMessage!.contains('timed out') || _errorMessage!.contains('timeout')) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSmall),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info, color: AppTheme.warningColor, size: 16),
                    SizedBox(width: AppTheme.spacingSmall),
                    Expanded(
                      child: Text(
                        'Tip: Check your internet connection or try a different city name',
                        style: TextStyle(
                          color: AppTheme.warningColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: AppTheme.spacingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _loadWeatherDataForCity(_cityController.text.trim()),
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showSearchDialog,
                    icon: const Icon(Icons.search),
                    label: const Text('Search City'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor,
                    ),
                  ),
                ),
              ],
            ),

            // Network test button for timeout errors
            if (_errorMessage!.contains('timed out') || _errorMessage!.contains('timeout')) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _testNetworkConnection,
                  icon: const Icon(Icons.network_check),
                  label: const Text('Test Network Connection'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentWeatherCard() {
    final weather = _currentWeather!;
    final tempUnit = _weatherPreference?.temperatureUnit ?? AppConstants.defaultTemperatureUnit;

    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(int.parse('0xFF${weather.weatherColor.substring(1)}')).withOpacity(0.3),
              Color(int.parse('0xFF${weather.weatherColor.substring(1)}')).withOpacity(0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            Text(
              '${weather.cityName}, ${weather.country}',
              style: AppTheme.headingMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              AppUtils.formatDateTime(weather.dateTime),
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.network(
                  weather.iconUrl,
                  width: 80,
                  height: 80,
                  errorBuilder: (context, error, stackTrace) {
                    return const FaIcon(
                      FontAwesomeIcons.cloudSun,
                      size: 60,
                      color: AppTheme.primaryColor,
                    );
                  },
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      weather.getFormattedTemperature(tempUnit),
                      style: AppTheme.headingLarge.copyWith(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      AppUtils.capitalize(weather.description),
                      style: AppTheme.bodyLarge,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Feels like ${AppUtils.formatTemperature(weather.feelsLike, tempUnit)}',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeatherDetailsCard() {
    final weather = _currentWeather!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weather Details',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    icon: FontAwesomeIcons.droplet,
                    label: 'Humidity',
                    value: '${weather.humidity}%',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    icon: FontAwesomeIcons.wind,
                    label: 'Wind Speed',
                    value: '${weather.windSpeed.toStringAsFixed(1)} m/s',
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    icon: FontAwesomeIcons.gaugeHigh,
                    label: 'Pressure',
                    value: '${weather.pressure} hPa',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    icon: weather.visibility != null ? FontAwesomeIcons.eye : FontAwesomeIcons.cloud,
                    label: weather.visibility != null ? 'Visibility' : 'Cloudiness',
                    value: weather.visibility != null
                        ? '${(weather.visibility! / 1000).toStringAsFixed(1)} km'
                        : '${weather.cloudiness ?? 0}%',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        FaIcon(
          icon,
          color: AppTheme.primaryColor,
          size: 24,
        ),
        const SizedBox(height: AppTheme.spacingSmall),
        Text(
          value,
          style: AppTheme.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildForecastCard() {
    final forecast = _weatherForecast!;
    final dailyForecasts = forecast.dailyForecasts.take(5).toList();
    final tempUnit = _weatherPreference?.temperatureUnit ?? AppConstants.defaultTemperatureUnit;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '5-Day Forecast',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: dailyForecasts.length,
                itemBuilder: (context, index) {
                  final dayForecast = dailyForecasts[index];
                  final isToday = index == 0;

                  return Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: AppTheme.spacingSmall),
                    child: Column(
                      children: [
                        Text(
                          isToday ? 'Today' : AppUtils.formatDate(dayForecast.dateTime, pattern: 'EEE'),
                          style: AppTheme.bodySmall.copyWith(
                            fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingSmall),
                        Image.network(
                          dayForecast.iconUrl,
                          width: 40,
                          height: 40,
                          errorBuilder: (context, error, stackTrace) {
                            return const FaIcon(
                              FontAwesomeIcons.cloudSun,
                              size: 30,
                              color: AppTheme.primaryColor,
                            );
                          },
                        ),
                        const SizedBox(height: AppTheme.spacingSmall),
                        Text(
                          dayForecast.getFormattedTemperature(tempUnit),
                          style: AppTheme.bodySmall.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingXLarge),
        child: Column(
          children: [
            const FaIcon(
              FontAwesomeIcons.cloudSun,
              size: 80,
              color: AppTheme.textHintColor,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            Text(
              'No Weather Data',
              style: AppTheme.headingMedium.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            const Text(
              'Search for a city to get weather information',
              style: AppTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            ElevatedButton.icon(
              onPressed: _showSearchDialog,
              icon: const Icon(Icons.search),
              label: const Text('Search City'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacingXLarge),
        child: Column(
          children: [
            SpinKitThreeBounce(
              color: AppTheme.primaryColor,
              size: 30.0,
            ),
            SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Loading weather data...',
              style: AppTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search City'),
        content: TextField(
          controller: _cityController,
          decoration: const InputDecoration(
            hintText: 'Enter city name',
            prefixIcon: Icon(Icons.location_city),
          ),
          onSubmitted: (_) {
            Navigator.of(context).pop();
            _searchCity();
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _searchCity();
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  Future<void> _testNetworkConnection() async {
    AppUtils.showLoadingDialog(context, message: 'Testing network connection...');

    try {
      final hasConnection = await _weatherService.hasInternetConnection();

      if (mounted) {
        AppUtils.hideLoadingDialog(context);

        if (hasConnection) {
          // Test API key
          final apiKeyValid = await _weatherService.testApiKey();

          if (apiKeyValid) {
            AppUtils.showSnackBar(
              context,
              'Network connection is good. API key is valid. Try searching for a common city like "London".',
            );
          } else {
            AppUtils.showSnackBar(
              context,
              'Network is connected but API key may be invalid. Please check your OpenWeatherMap API key.',
              isError: true,
            );
          }
        } else {
          AppUtils.showSnackBar(
            context,
            'No internet connection detected. Please check your network settings.',
            isError: true,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.hideLoadingDialog(context);
        AppUtils.showSnackBar(
          context,
          'Network test failed: ${e.toString()}',
          isError: true,
        );
      }
    }
  }
}
