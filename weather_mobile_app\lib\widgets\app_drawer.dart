import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../utils/app_utils.dart';
import '../services/auth_service.dart';

class AppDrawer extends StatelessWidget {
  final String currentRoute;
  final String userName;
  final String userEmail;
  final String? userAvatar;

  const AppDrawer({
    super.key,
    required this.currentRoute,
    required this.userName,
    required this.userEmail,
    this.userAvatar,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          _buildDrawerHeader(context),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildMenuItem(
                  context,
                  title: 'Dashboard',
                  icon: FontAwesomeIcons.chartLine,
                  route: AppConstants.dashboardRoute,
                ),
                _buildMenuItem(
                  context,
                  title: 'Weather',
                  icon: FontAwesomeIcons.cloudSun,
                  route: AppConstants.mainRoute,
                ),
                _buildMenuItem(
                  context,
                  title: 'Reports',
                  icon: FontAwesomeIcons.fileLines,
                  route: AppConstants.reportRoute,
                ),
                const Divider(),
                _buildMenuItem(
                  context,
                  title: 'Settings',
                  icon: FontAwesomeIcons.gear,
                  route: AppConstants.settingsRoute,
                ),
                _buildMenuItem(
                  context,
                  title: 'Account',
                  icon: FontAwesomeIcons.user,
                  route: AppConstants.accountRoute,
                ),
                _buildMenuItem(
                  context,
                  title: 'Contact',
                  icon: FontAwesomeIcons.envelope,
                  route: AppConstants.contactRoute,
                ),
                const Divider(),
                _buildLogoutItem(context),
              ],
            ),
          ),
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Container(
      height: 200,
      decoration: const BoxDecoration(
        gradient: AppTheme.primaryGradient,
      ),
      child: DrawerHeader(
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 35,
              backgroundColor: Colors.white,
              backgroundImage: userAvatar != null 
                  ? NetworkImage(userAvatar!) 
                  : null,
              child: userAvatar == null
                  ? Text(
                      AppUtils.getInitials(userName),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    )
                  : null,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              userName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              userEmail,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required String route,
  }) {
    final isSelected = currentRoute == route;
    
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : null,
      ),
      child: ListTile(
        leading: FaIcon(
          icon,
          color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
          size: 20,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimaryColor,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onTap: () {
          Navigator.of(context).pop(); // Close drawer
          if (!isSelected) {
            Navigator.of(context).pushReplacementNamed(route);
          }
        },
      ),
    );
  }

  Widget _buildLogoutItem(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSmall,
        vertical: 2,
      ),
      child: ListTile(
        leading: const FaIcon(
          FontAwesomeIcons.rightFromBracket,
          color: AppTheme.errorColor,
          size: 20,
        ),
        title: const Text(
          'Logout',
          style: TextStyle(
            color: AppTheme.errorColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        onTap: () => _handleLogout(context),
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    Navigator.of(context).pop(); // Close drawer first

    final shouldLogout = await AppUtils.showConfirmationDialog(
      context,
      title: 'Logout',
      message: 'Are you sure you want to logout? This will clear all your session data.',
      confirmText: 'Logout',
      cancelText: 'Cancel',
    );

    if (shouldLogout && context.mounted) {
      // Show loading indicator
      AppUtils.showLoadingDialog(context, message: 'Logging out...');

      try {
        // Perform logout using AuthService
        final success = await AuthService.instance.logout();

        if (context.mounted) {
          AppUtils.hideLoadingDialog(context);

          if (success) {
            // Show success message
            AppUtils.showSnackBar(
              context,
              'Logged out successfully',
            );

            // Navigate to login screen and clear all previous routes
            Navigator.of(context).pushNamedAndRemoveUntil(
              AppConstants.loginRoute,
              (route) => false,
            );
          } else {
            // Show error message
            AppUtils.showSnackBar(
              context,
              'Failed to logout. Please try again.',
              isError: true,
            );
          }
        }
      } catch (e) {
        if (context.mounted) {
          AppUtils.hideLoadingDialog(context);
          AppUtils.showSnackBar(
            context,
            'An error occurred during logout.',
            isError: true,
          );
        }
      }
    }
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const FaIcon(
                FontAwesomeIcons.cloudSun,
                color: AppTheme.primaryColor,
                size: 16,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                AppConstants.appName,
                style: AppTheme.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Version ${AppConstants.appVersion}',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textHintColor,
            ),
          ),
        ],
      ),
    );
  }
}
