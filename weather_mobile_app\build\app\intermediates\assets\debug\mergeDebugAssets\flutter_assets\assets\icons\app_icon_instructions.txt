App Icon Instructions
====================

To create a custom launcher icon for the Weather Mobile App:

1. Create an app icon image (1024x1024 pixels) with the following design:
   - Background: Blue gradient (similar to app theme)
   - Icon: Weather symbol (cloud with sun)
   - Colors: Use app theme colors (primary blue #1976D2)

2. Save the icon as 'app_icon.png' in this directory

3. Use flutter_launcher_icons package to generate platform-specific icons:
   
   Add to pubspec.yaml:
   ```yaml
   dev_dependencies:
     flutter_launcher_icons: ^0.13.1
   
   flutter_launcher_icons:
     android: "launcher_icon"
     ios: true
     image_path: "assets/icons/app_icon.png"
     min_sdk_android: 21
   ```

4. Run the icon generation command:
   ```bash
   flutter pub get
   flutter pub run flutter_launcher_icons:main
   ```

For now, the app will use the default Flutter icon until a custom icon is provided.

Design Guidelines:
- Keep the design simple and recognizable
- Use weather-related symbols (cloud, sun, thermometer)
- Ensure good contrast and visibility at small sizes
- Follow platform-specific design guidelines (Material Design for Android, Human Interface Guidelines for iOS)
