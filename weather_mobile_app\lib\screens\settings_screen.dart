import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../widgets/app_drawer.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../models/weather_preference_model.dart';
import '../utils/app_utils.dart';
import '../services/auth_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _databaseHelper = DatabaseHelper();
  UserModel? _currentUser;
  WeatherPreferenceModel? _weatherPreference;
  bool _isLoading = true;

  // Settings values
  String _temperatureUnit = AppConstants.defaultTemperatureUnit;
  bool _notificationsEnabled = true;
  bool _locationEnabled = false;
  int _refreshInterval = 30;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);

      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
        _weatherPreference = await _databaseHelper.getWeatherPreferenceByUserId(userId);

        if (_weatherPreference != null) {
          setState(() {
            _temperatureUnit = _weatherPreference!.temperatureUnit;
            _notificationsEnabled = _weatherPreference!.notificationsEnabled;
            _locationEnabled = _weatherPreference!.locationEnabled;
            _refreshInterval = _weatherPreference!.refreshInterval;
          });
        }
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to load settings',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateWeatherPreference() async {
    if (_weatherPreference == null || _currentUser == null) return;

    try {
      final updatedPreference = _weatherPreference!.copyWith(
        temperatureUnit: _temperatureUnit,
        notificationsEnabled: _notificationsEnabled,
        locationEnabled: _locationEnabled,
        refreshInterval: _refreshInterval,
        updatedAt: DateTime.now(),
      );

      await _databaseHelper.updateWeatherPreference(updatedPreference);
      _weatherPreference = updatedPreference;

      AppUtils.showSnackBar(
        context,
        'Settings updated successfully',
      );
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to update settings',
        isError: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _updateWeatherPreference,
          ),
        ],
      ),
      drawer: _currentUser != null
          ? AppDrawer(
              currentRoute: AppConstants.settingsRoute,
              userName: _currentUser!.fullName,
              userEmail: _currentUser!.email,
              userAvatar: _currentUser!.profileImage,
            )
          : null,
      body: ListView(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        children: [
          _buildWeatherSettingsSection(),
          const SizedBox(height: AppTheme.spacingLarge),
          _buildNotificationSettingsSection(),
          const SizedBox(height: AppTheme.spacingLarge),
          _buildAppSettingsSection(),
          const SizedBox(height: AppTheme.spacingLarge),
          _buildAboutSection(),
        ],
      ),
    );
  }

  Widget _buildWeatherSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.cloudSun,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Weather Settings',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Temperature Unit
            ListTile(
              title: const Text('Temperature Unit'),
              subtitle: Text(_getTemperatureUnitDisplayName(_temperatureUnit)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showTemperatureUnitDialog,
            ),

            const Divider(),

            // Location Services
            SwitchListTile(
              title: const Text('Location Services'),
              subtitle: const Text('Use GPS for weather location'),
              value: _locationEnabled,
              onChanged: (value) {
                setState(() {
                  _locationEnabled = value;
                });
              },
              secondary: const Icon(Icons.location_on),
            ),

            const Divider(),

            // Refresh Interval
            ListTile(
              title: const Text('Auto Refresh'),
              subtitle: Text('Every $_refreshInterval minutes'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showRefreshIntervalDialog,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.bell,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Notifications',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Weather Notifications
            SwitchListTile(
              title: const Text('Weather Alerts'),
              subtitle: const Text('Receive weather notifications'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
              secondary: const Icon(Icons.notifications),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.gear,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'App Settings',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            ListTile(
              title: const Text('Clear Cache'),
              subtitle: const Text('Clear app cache and temporary data'),
              leading: const Icon(Icons.clear_all),
              onTap: _showClearCacheDialog,
            ),

            const Divider(),

            ListTile(
              title: const Text('Reset Settings'),
              subtitle: const Text('Reset all settings to default'),
              leading: const Icon(Icons.restore),
              onTap: _showResetSettingsDialog,
            ),

            const Divider(),

            ListTile(
              title: const Text('Privacy Policy'),
              subtitle: const Text('View our privacy policy'),
              leading: const Icon(Icons.privacy_tip),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => Navigator.of(context).pushNamed(AppConstants.privacyPolicyRoute),
            ),

            ListTile(
              title: const Text('Terms of Service'),
              subtitle: const Text('View our terms of service'),
              leading: const Icon(Icons.description),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => Navigator.of(context).pushNamed(AppConstants.termsOfServiceRoute),
            ),

            ListTile(
              title: const Text('Logout'),
              subtitle: const Text('Sign out of your account'),
              leading: const Icon(Icons.logout, color: AppTheme.errorColor),
              onTap: _handleLogout,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.info,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'About',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            ListTile(
              title: const Text('App Version'),
              subtitle: Text(AppConstants.appVersion),
              leading: const Icon(Icons.info_outline),
            ),

            const Divider(),

            ListTile(
              title: const Text('Privacy Policy'),
              leading: const Icon(Icons.privacy_tip),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Show privacy policy
              },
            ),

            const Divider(),

            ListTile(
              title: const Text('Terms of Service'),
              leading: const Icon(Icons.description),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Show terms of service
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getTemperatureUnitDisplayName(String unit) {
    switch (unit) {
      case AppConstants.celsius:
        return 'Celsius (°C)';
      case AppConstants.fahrenheit:
        return 'Fahrenheit (°F)';
      case AppConstants.kelvin:
        return 'Kelvin (K)';
      default:
        return 'Celsius (°C)';
    }
  }

  void _showTemperatureUnitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Temperature Unit'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Celsius (°C)'),
              value: AppConstants.celsius,
              groupValue: _temperatureUnit,
              onChanged: (value) {
                setState(() {
                  _temperatureUnit = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('Fahrenheit (°F)'),
              value: AppConstants.fahrenheit,
              groupValue: _temperatureUnit,
              onChanged: (value) {
                setState(() {
                  _temperatureUnit = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('Kelvin (K)'),
              value: AppConstants.kelvin,
              groupValue: _temperatureUnit,
              onChanged: (value) {
                setState(() {
                  _temperatureUnit = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showRefreshIntervalDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auto Refresh Interval'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<int>(
              title: const Text('15 minutes'),
              value: 15,
              groupValue: _refreshInterval,
              onChanged: (value) {
                setState(() {
                  _refreshInterval = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<int>(
              title: const Text('30 minutes'),
              value: 30,
              groupValue: _refreshInterval,
              onChanged: (value) {
                setState(() {
                  _refreshInterval = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<int>(
              title: const Text('1 hour'),
              value: 60,
              groupValue: _refreshInterval,
              onChanged: (value) {
                setState(() {
                  _refreshInterval = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<int>(
              title: const Text('2 hours'),
              value: 120,
              groupValue: _refreshInterval,
              onChanged: (value) {
                setState(() {
                  _refreshInterval = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearCacheDialog() {
    AppUtils.showConfirmationDialog(
      context,
      title: 'Clear Cache',
      message: 'Are you sure you want to clear the app cache? This will remove temporary data.',
      confirmText: 'Clear',
    ).then((confirmed) {
      if (confirmed) {
        AppUtils.showSnackBar(
          context,
          'Cache cleared successfully',
        );
      }
    });
  }

  void _showResetSettingsDialog() {
    AppUtils.showConfirmationDialog(
      context,
      title: 'Reset Settings',
      message: 'Are you sure you want to reset all settings to default? This action cannot be undone.',
      confirmText: 'Reset',
    ).then((confirmed) {
      if (confirmed) {
        setState(() {
          _temperatureUnit = AppConstants.defaultTemperatureUnit;
          _notificationsEnabled = true;
          _locationEnabled = false;
          _refreshInterval = 30;
        });
        _updateWeatherPreference();
      }
    });
  }

  Future<void> _handleLogout() async {
    final shouldLogout = await AppUtils.showConfirmationDialog(
      context,
      title: 'Logout',
      message: 'Are you sure you want to logout? This will sign you out of your account.',
      confirmText: 'Logout',
      cancelText: 'Cancel',
    );

    if (shouldLogout && mounted) {
      // Show loading indicator
      AppUtils.showLoadingDialog(context, message: 'Logging out...');

      try {
        // Perform logout using AuthService
        final success = await AuthService.instance.logout();

        if (mounted) {
          AppUtils.hideLoadingDialog(context);

          if (success) {
            // Show success message
            AppUtils.showSnackBar(
              context,
              'Logged out successfully',
            );

            // Navigate to login screen and clear all previous routes
            Navigator.of(context).pushNamedAndRemoveUntil(
              AppConstants.loginRoute,
              (route) => false,
            );
          } else {
            // Show error message
            AppUtils.showSnackBar(
              context,
              'Failed to logout. Please try again.',
              isError: true,
            );
          }
        }
      } catch (e) {
        if (mounted) {
          AppUtils.hideLoadingDialog(context);
          AppUtils.showSnackBar(
            context,
            'An error occurred during logout.',
            isError: true,
          );
        }
      }
    }
  }
}
