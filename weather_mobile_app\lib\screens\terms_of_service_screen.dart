import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildLastUpdated(),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildSection(
              title: '1. Acceptance of Terms',
              content: _getAcceptanceContent(),
            ),
            _buildSection(
              title: '2. Description of Service',
              content: _getServiceDescriptionContent(),
            ),
            _buildSection(
              title: '3. User Accounts and Registration',
              content: _getUserAccountsContent(),
            ),
            _buildSection(
              title: '4. Acceptable Use Policy',
              content: _getAcceptableUseContent(),
            ),
            _buildSection(
              title: '5. Weather Data and Accuracy',
              content: _getWeatherDataContent(),
            ),
            _buildSection(
              title: '6. User-Generated Content',
              content: _getUserContentContent(),
            ),
            _buildSection(
              title: '7. Privacy and Data Protection',
              content: _getPrivacyContent(),
            ),
            _buildSection(
              title: '8. Intellectual Property Rights',
              content: _getIntellectualPropertyContent(),
            ),
            _buildSection(
              title: '9. Disclaimers and Limitations',
              content: _getDisclaimersContent(),
            ),
            _buildSection(
              title: '10. Termination',
              content: _getTerminationContent(),
            ),
            _buildSection(
              title: '11. Updates and Modifications',
              content: _getUpdatesContent(),
            ),
            _buildSection(
              title: '12. Contact Information',
              content: _getContactContent(),
            ),
            const SizedBox(height: AppTheme.spacingXLarge),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          children: [
            const FaIcon(
              FontAwesomeIcons.fileContract,
              size: 48,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Terms of Service',
              style: AppTheme.headingLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            const Text(
              'Please read these terms carefully before using Weather Mobile App. By using our app, you agree to be bound by these terms.',
              style: AppTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLastUpdated() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AppTheme.infoColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(color: AppTheme.infoColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const FaIcon(
            FontAwesomeIcons.calendar,
            size: 16,
            color: AppTheme.infoColor,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            'Last Updated: ${_getFormattedDate()}',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.infoColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              content,
              style: AppTheme.bodyMedium,
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Column(
        children: [
          const FaIcon(
            FontAwesomeIcons.handshake,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Thank you for using Weather Mobile App',
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          const Text(
            'By using our app, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.',
            style: AppTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getFormattedDate() {
    final now = DateTime.now();
    return '${now.day}/${now.month}/${now.year}';
  }

  String _getAcceptanceContent() {
    return 'By downloading, installing, or using Weather Mobile App ("the App"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, please do not use the App.\n\n'
        'These Terms constitute a legally binding agreement between you and Weather Mobile App. Your use of the App signifies your acceptance of these Terms and our Privacy Policy.\n\n'
        'If you are using the App on behalf of an organization, you represent that you have the authority to bind that organization to these Terms.';
  }

  String _getServiceDescriptionContent() {
    return 'Weather Mobile App is a comprehensive weather application that provides:\n\n'
        '• Real-time weather information and forecasts\n'
        '• Weather report creation and management\n'
        '• Personalized weather preferences and notifications\n'
        '• User account management and data storage\n'
        '• Weather-related feedback and observation tools\n\n'
        'The App integrates with third-party weather services (OpenWeatherMap) to provide accurate and up-to-date weather information. We strive to provide reliable service but cannot guarantee uninterrupted availability.';
  }

  String _getUserAccountsContent() {
    return 'To access certain features of the App, you must create a user account:\n\n'
        '• Account Creation: You must provide accurate and complete information during registration\n'
        '• Account Security: You are responsible for maintaining the confidentiality of your account credentials\n'
        '• Account Responsibility: You are responsible for all activities that occur under your account\n'
        '• Age Requirement: You must be at least 13 years old to create an account\n'
        '• One Account: You may only create one account per person\n\n'
        'You must immediately notify us of any unauthorized use of your account or any other breach of security.';
  }

  String _getAcceptableUseContent() {
    return 'You agree to use the App in accordance with these guidelines:\n\n'
        'Permitted Uses:\n'
        '• Access weather information for personal or business purposes\n'
        '• Create and manage weather reports and observations\n'
        '• Customize your weather experience through app settings\n'
        '• Provide feedback to help improve the App\n\n'
        'Prohibited Uses:\n'
        '• Submit false, misleading, or harmful weather reports\n'
        '• Attempt to gain unauthorized access to the App or other users\' accounts\n'
        '• Use the App for any illegal or unauthorized purpose\n'
        '• Interfere with or disrupt the App\'s functionality\n'
        '• Reverse engineer, decompile, or attempt to extract source code';
  }

  String _getWeatherDataContent() {
    return 'Weather information provided by the App is sourced from third-party services:\n\n'
        '• Data Sources: Weather data is primarily sourced from OpenWeatherMap API and other reliable meteorological services\n'
        '• Accuracy Disclaimer: While we strive for accuracy, weather data may not always be 100% accurate or up-to-date\n'
        '• No Liability: We are not liable for decisions made based on weather information provided by the App\n'
        '• Emergency Situations: Do not rely solely on the App for severe weather warnings or emergency situations\n'
        '• Official Sources: Always consult official meteorological services for critical weather decisions\n\n'
        'Weather conditions can change rapidly, and forecasts are predictions that may not always be accurate.';
  }

  String _getUserContentContent() {
    return 'When you submit content through the App (reports, feedback, observations):\n\n'
        '• Content Ownership: You retain ownership of the content you create\n'
        '• License Grant: You grant us a non-exclusive license to use your content to improve the App\n'
        '• Content Standards: All content must be accurate, respectful, and comply with applicable laws\n'
        '• Prohibited Content: No spam, offensive material, false information, or copyrighted content\n'
        '• Content Removal: We reserve the right to remove content that violates these Terms\n'
        '• Backup Responsibility: You are responsible for backing up your important content\n\n'
        'We may use anonymized and aggregated user content to improve weather predictions and app functionality.';
  }

  String _getPrivacyContent() {
    return 'Your privacy is important to us:\n\n'
        '• Privacy Policy: Our detailed Privacy Policy explains how we collect, use, and protect your information\n'
        '• Local Storage: Most of your data is stored locally on your device using encrypted databases\n'
        '• Data Minimization: We collect only the minimum data necessary to provide our services\n'
        '• Third-Party Services: Some features may involve third-party services with their own privacy policies\n'
        '• Data Control: You have control over your data and can delete your account at any time\n'
        '• Security Measures: We implement appropriate security measures to protect your information\n\n'
        'Please review our Privacy Policy for complete details about our data practices.';
  }

  String _getIntellectualPropertyContent() {
    return 'Intellectual property rights are protected:\n\n'
        '• App Ownership: Weather Mobile App, including its design, code, and features, is our intellectual property\n'
        '• Trademarks: All trademarks, logos, and brand names are owned by their respective owners\n'
        '• User License: We grant you a limited, non-exclusive license to use the App for personal purposes\n'
        '• Restrictions: You may not copy, modify, distribute, or create derivative works of the App\n'
        '• Third-Party Content: Some content may be owned by third parties and subject to their terms\n'
        '• DMCA Compliance: We respect intellectual property rights and respond to valid DMCA notices\n\n'
        'Any unauthorized use of our intellectual property may result in legal action.';
  }

  String _getDisclaimersContent() {
    return 'Important disclaimers and limitations:\n\n'
        '• Service Availability: The App is provided "as is" without warranties of any kind\n'
        '• No Guarantees: We do not guarantee that the App will be error-free or continuously available\n'
        '• Weather Accuracy: Weather information is provided for general purposes and may not be accurate\n'
        '• Limitation of Liability: Our liability is limited to the maximum extent permitted by law\n'
        '• Third-Party Services: We are not responsible for third-party services or content\n'
        '• Force Majeure: We are not liable for delays or failures due to circumstances beyond our control\n\n'
        'Use the App at your own risk and always verify critical information through official sources.';
  }

  String _getTerminationContent() {
    return 'Account termination and suspension:\n\n'
        '• User Termination: You may delete your account at any time through the App settings\n'
        '• Our Right to Terminate: We may suspend or terminate accounts that violate these Terms\n'
        '• Effect of Termination: Upon termination, your access to the App will cease\n'
        '• Data Retention: We may retain some data as required by law or for legitimate business purposes\n'
        '• Survival: Certain provisions of these Terms will survive termination\n\n'
        'Termination does not affect any rights or obligations that arose before termination.';
  }

  String _getUpdatesContent() {
    return 'Terms and App updates:\n\n'
        '• Terms Updates: We may update these Terms from time to time\n'
        '• Notification: We will notify you of significant changes through the App or email\n'
        '• Continued Use: Your continued use of the App after changes constitutes acceptance\n'
        '• App Updates: We may release updates to improve functionality and security\n'
        '• Automatic Updates: Some updates may be installed automatically\n'
        '• Version Requirements: Older versions may not be supported\n\n'
        'We recommend keeping the App updated to ensure the best experience and security.';
  }

  String _getContactContent() {
    return 'For questions about these Terms of Service:\n\n'
        '• Email: <EMAIL>\n'
        '• Phone: +255765098595\n'
        '• In-App Contact: Use the contact section within the App\n\n'
        'We will respond to your inquiries promptly and work to resolve any concerns you may have about these Terms.';
  }
}
