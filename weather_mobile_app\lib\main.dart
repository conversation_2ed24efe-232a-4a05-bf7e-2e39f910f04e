import 'package:flutter/material.dart';
import 'config/app_theme.dart';
import 'config/app_constants.dart';
import 'screens/splash_screen.dart';
import 'screens/login_screen.dart';
import 'screens/signup_screen.dart';
import 'screens/reset_password_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/main_screen.dart';
import 'screens/report_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/account_screen.dart';
import 'screens/contact_screen.dart';
import 'screens/privacy_policy_screen.dart';
import 'screens/terms_of_service_screen.dart';

void main() {
  runApp(const WeatherMobileApp());
}

class WeatherMobileApp extends StatelessWidget {
  const WeatherMobileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      debugShowCheckedModeBanner: false,
      initialRoute: AppConstants.splashRoute,
      routes: {
        AppConstants.splashRoute: (context) => const SplashScreen(),
        AppConstants.loginRoute: (context) => const LoginScreen(),
        AppConstants.signUpRoute: (context) => const SignUpScreen(),
        AppConstants.resetPasswordRoute: (context) => const ResetPasswordScreen(),
        AppConstants.dashboardRoute: (context) => const DashboardScreen(),
        AppConstants.mainRoute: (context) => const MainScreen(),
        AppConstants.reportRoute: (context) => const ReportScreen(),
        AppConstants.settingsRoute: (context) => const SettingsScreen(),
        AppConstants.accountRoute: (context) => const AccountScreen(),
        AppConstants.contactRoute: (context) => const ContactScreen(),
        AppConstants.privacyPolicyRoute: (context) => const PrivacyPolicyScreen(),
        AppConstants.termsOfServiceRoute: (context) => const TermsOfServiceScreen(),
      },
    );
  }
}


