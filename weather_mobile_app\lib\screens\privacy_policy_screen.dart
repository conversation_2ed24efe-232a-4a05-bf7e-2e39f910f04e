import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildLastUpdated(),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildSection(
              title: '1. Information We Collect',
              content: _getInformationCollectionContent(),
            ),
            _buildSection(
              title: '2. How We Use Your Information',
              content: _getInformationUsageContent(),
            ),
            _buildSection(
              title: '3. Data Storage and Security',
              content: _getDataStorageContent(),
            ),
            _buildSection(
              title: '4. Third-Party Services',
              content: _getThirdPartyContent(),
            ),
            _buildSection(
              title: '5. Your Rights',
              content: _getUserRightsContent(),
            ),
            _buildSection(
              title: '6. Data Retention',
              content: _getDataRetentionContent(),
            ),
            _buildSection(
              title: '7. Children\'s Privacy',
              content: _getChildrenPrivacyContent(),
            ),
            _buildSection(
              title: '8. Changes to This Policy',
              content: _getPolicyChangesContent(),
            ),
            _buildSection(
              title: '9. Contact Us',
              content: _getContactContent(),
            ),
            const SizedBox(height: AppTheme.spacingXLarge),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          children: [
            const FaIcon(
              FontAwesomeIcons.shieldHalved,
              size: 48,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Privacy Policy',
              style: AppTheme.headingLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            const Text(
              'Your privacy is important to us. This policy explains how we collect, use, and protect your information.',
              style: AppTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLastUpdated() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AppTheme.infoColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(color: AppTheme.infoColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const FaIcon(
            FontAwesomeIcons.calendar,
            size: 16,
            color: AppTheme.infoColor,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            'Last Updated: ${_getFormattedDate()}',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.infoColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              content,
              style: AppTheme.bodyMedium,
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Column(
        children: [
          const FaIcon(
            FontAwesomeIcons.handshake,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Thank you for trusting us with your data',
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          const Text(
            'We are committed to protecting your privacy and ensuring the security of your personal information.',
            style: AppTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getFormattedDate() {
    final now = DateTime.now();
    return '${now.day}/${now.month}/${now.year}';
  }

  String _getInformationCollectionContent() {
    return 'We collect information you provide directly to us, such as when you create an account, submit reports, or contact us for support. This includes:\n\n'
        '• Account information (name, email address)\n'
        '• Report data (titles, descriptions, locations)\n'
        '• Weather preferences and settings\n'
        '• Device information for app functionality\n\n'
        'All data is stored locally on your device and is not transmitted to external servers without your explicit consent.';
  }

  String _getInformationUsageContent() {
    return 'We use the information we collect to:\n\n'
        '• Provide and maintain the app functionality\n'
        '• Process and manage your reports\n'
        '• Customize your weather experience\n'
        '• Improve our services and user experience\n'
        '• Communicate with you about app updates\n\n'
        'We do not sell, trade, or otherwise transfer your personal information to third parties.';
  }

  String _getDataStorageContent() {
    return 'Your data is primarily stored locally on your device using SQLite database. We implement appropriate security measures to protect your information:\n\n'
        '• Local encryption for sensitive data\n'
        '• Secure authentication mechanisms\n'
        '• Regular security updates\n'
        '• Limited data collection practices\n\n'
        'Weather data is fetched from OpenWeatherMap API and is not stored permanently.';
  }

  String _getThirdPartyContent() {
    return 'This app uses the following third-party services:\n\n'
        '• OpenWeatherMap API for weather data\n'
        '• Flutter framework and associated packages\n\n'
        'These services have their own privacy policies. We encourage you to review their privacy practices. We do not share your personal information with these services beyond what is necessary for app functionality.';
  }

  String _getUserRightsContent() {
    return 'You have the following rights regarding your personal information:\n\n'
        '• Access: View your stored data through the app\n'
        '• Correction: Update your information in settings\n'
        '• Deletion: Delete your account and all associated data\n'
        '• Portability: Export your data (contact us for assistance)\n'
        '• Withdrawal: Opt out of data collection features\n\n'
        'To exercise these rights, use the app settings or contact us directly.';
  }

  String _getDataRetentionContent() {
    return 'We retain your information for as long as your account is active or as needed to provide services. You can delete your account and all associated data at any time through the app settings.\n\n'
        'When you delete your account:\n'
        '• All personal data is permanently removed\n'
        '• Reports and preferences are deleted\n'
        '• Local database is cleared\n\n'
        'Some anonymized usage statistics may be retained for app improvement purposes.';
  }

  String _getChildrenPrivacyContent() {
    return 'This app is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.\n\n'
        'If you are a parent or guardian and believe your child has provided us with personal information, please contact us immediately. We will take steps to remove such information from our systems.';
  }

  String _getPolicyChangesContent() {
    return 'We may update this Privacy Policy from time to time. We will notify you of any changes by:\n\n'
        '• Posting the new policy in the app\n'
        '• Updating the "Last Updated" date\n'
        '• Providing in-app notifications for significant changes\n\n'
        'Your continued use of the app after changes constitutes acceptance of the updated policy.';
  }

  String _getContactContent() {
    return 'If you have any questions about this Privacy Policy or our data practices, please contact us:\n\n'
        '• Through the app\'s contact section\n'
        '• Email: <EMAIL>\n'
        '• Phone: +255765098595\n'
        '• Address: Weather App Privacy Team\n\n'
        'We will respond to your inquiries within 30 days and work to resolve any privacy concerns you may have.';
  }
}
