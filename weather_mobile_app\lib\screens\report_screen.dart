import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../widgets/app_drawer.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../models/report_model.dart';
import '../utils/app_utils.dart';
import 'create_report_screen.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({super.key});

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> with TickerProviderStateMixin {
  final _databaseHelper = DatabaseHelper();
  late TabController _tabController;

  UserModel? _currentUser;
  List<ReportModel> _allReports = [];
  List<ReportModel> _filteredReports = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';
  bool _showCreateForm = false;

  // Form controllers for inline report creation
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  String _newReportCategory = ReportCategory.weather;
  String _newReportPriority = ReportPriority.medium;
  String _newReportStatus = ReportStatus.pending;
  DateTime _newReportDate = DateTime.now();
  bool _isCreatingReport = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);

      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
        await _loadReports();
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to load user data',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadReports() async {
    if (_currentUser == null) return;

    try {
      final reports = await _databaseHelper.getReportsByUserId(_currentUser!.id!);
      setState(() {
        _allReports = reports;
        _applyFilter();
      });
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to load reports',
        isError: true,
      );
    }
  }

  void _applyFilter() {
    setState(() {
      if (_selectedFilter == 'all') {
        _filteredReports = _allReports;
      } else {
        _filteredReports = _allReports.where((report) => report.status == _selectedFilter).toList();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'My Reports', icon: Icon(Icons.list)),
            Tab(text: 'Create Report', icon: Icon(Icons.add)),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedFilter = value;
                _applyFilter();
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('All Reports')),
              const PopupMenuItem(value: 'pending', child: Text('Pending')),
              const PopupMenuItem(value: 'in_progress', child: Text('In Progress')),
              const PopupMenuItem(value: 'completed', child: Text('Completed')),
            ],
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      drawer: _currentUser != null
          ? AppDrawer(
              currentRoute: AppConstants.reportRoute,
              userName: _currentUser!.fullName,
              userEmail: _currentUser!.email,
              userAvatar: _currentUser!.profileImage,
            )
          : null,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildReportsListTab(),
          _buildCreateReportTab(),
        ],
      ),
    );
  }

  Widget _buildReportsListTab() {
    return RefreshIndicator(
      onRefresh: _loadReports,
      child: _filteredReports.isEmpty
          ? _buildEmptyReportsView()
          : ListView.builder(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              itemCount: _filteredReports.length,
              itemBuilder: (context, index) {
                final report = _filteredReports[index];
                return _buildReportCard(report);
              },
            ),
    );
  }

  Widget _buildEmptyReportsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const FaIcon(
            FontAwesomeIcons.fileLines,
            size: 80,
            color: AppTheme.textHintColor,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            _selectedFilter == 'all' ? 'No Reports Yet' : 'No ${_getFilterDisplayName()} Reports',
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          const Text(
            'Create your first report to get started',
            style: AppTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          ElevatedButton.icon(
            onPressed: () => _tabController.animateTo(1),
            icon: const Icon(Icons.add),
            label: const Text('Create Report'),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(ReportModel report) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: InkWell(
        onTap: () => _showReportDetails(report),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      report.title,
                      style: AppTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _editReport(report);
                          break;
                        case 'delete':
                          _deleteReport(report);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: AppTheme.errorColor),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: AppTheme.errorColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Color(int.parse('0xFF${report.statusColor.substring(1)}')).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                    ),
                    child: Text(
                      ReportStatus.getDisplayName(report.status),
                      style: AppTheme.bodySmall.copyWith(
                        color: Color(int.parse('0xFF${report.statusColor.substring(1)}')),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                report.description,
                style: AppTheme.bodyMedium,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Row(
                children: [
                  Icon(
                    Icons.category,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    ReportCategory.getDisplayName(report.category),
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingMedium),
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    AppUtils.formatDate(report.createdAt),
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingSmall,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Color(int.parse('0xFF${report.priorityColor.substring(1)}')).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                    ),
                    child: Text(
                      ReportPriority.getDisplayName(report.priority),
                      style: AppTheme.bodySmall.copyWith(
                        color: Color(int.parse('0xFF${report.priorityColor.substring(1)}')),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreateReportTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          // Toggle between form and templates
          if (!_showCreateForm) ...[
            // Quick Create Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingLarge),
                child: Column(
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.plus,
                      size: 60,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    Text(
                      'Create New Report',
                      style: AppTheme.headingMedium,
                    ),
                    const SizedBox(height: AppTheme.spacingSmall),
                    const Text(
                      'Report issues, feedback, or weather observations',
                      style: AppTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingLarge),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _showInlineCreateForm,
                            icon: const Icon(Icons.edit),
                            label: const Text('Create Report Here'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.all(AppTheme.spacingMedium),
                            ),
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _navigateToCreateReport,
                            icon: const Icon(Icons.open_in_new),
                            label: const Text('Full Screen'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.all(AppTheme.spacingMedium),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingLarge),

            // Quick Report Templates
            _buildQuickReportTemplates(),

            const SizedBox(height: AppTheme.spacingLarge),

            // Recent Reports Summary
            _buildRecentReportsSummary(),
          ] else ...[
            // Inline Create Form
            _buildInlineCreateForm(),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickReportTemplates() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Report Templates',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacingSmall,
              mainAxisSpacing: AppTheme.spacingSmall,
              childAspectRatio: 1.2,
              children: [
                _buildTemplateCard(
                  title: 'Weather Issue',
                  icon: FontAwesomeIcons.cloudRain,
                  color: AppTheme.rainColor,
                  category: ReportCategory.weather,
                ),
                _buildTemplateCard(
                  title: 'Technical Problem',
                  icon: FontAwesomeIcons.gear,
                  color: AppTheme.errorColor,
                  category: ReportCategory.technical,
                ),
                _buildTemplateCard(
                  title: 'Bug Report',
                  icon: FontAwesomeIcons.bug,
                  color: AppTheme.warningColor,
                  category: ReportCategory.bug,
                ),
                _buildTemplateCard(
                  title: 'Feedback',
                  icon: FontAwesomeIcons.comment,
                  color: AppTheme.successColor,
                  category: ReportCategory.feedback,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateCard({
    required String title,
    required IconData icon,
    required Color color,
    required String category,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _createQuickReport(category),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingSmall),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSmall),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: FaIcon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                title,
                style: AppTheme.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentReportsSummary() {
    final recentCount = _allReports.length > 5 ? 5 : _allReports.length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Report Summary',
                  style: AppTheme.headingSmall,
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(0),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Total',
                    _allReports.length.toString(),
                    AppTheme.infoColor,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Pending',
                    _allReports.where((r) => r.status == ReportStatus.pending).length.toString(),
                    AppTheme.warningColor,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Completed',
                    _allReports.where((r) => r.status == ReportStatus.completed).length.toString(),
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: AppTheme.headingMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTheme.bodySmall,
        ),
      ],
    );
  }

  String _getFilterDisplayName() {
    switch (_selectedFilter) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return 'All';
    }
  }

  void _showReportDetails(ReportModel report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(report.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Category: ${ReportCategory.getDisplayName(report.category)}'),
              const SizedBox(height: AppTheme.spacingSmall),
              Text('Priority: ${ReportPriority.getDisplayName(report.priority)}'),
              const SizedBox(height: AppTheme.spacingSmall),
              Text('Status: ${ReportStatus.getDisplayName(report.status)}'),
              const SizedBox(height: AppTheme.spacingSmall),
              Text('Created: ${AppUtils.formatDateTime(report.createdAt)}'),
              if (report.location != null) ...[
                const SizedBox(height: AppTheme.spacingSmall),
                Text('Location: ${report.location}'),
              ],
              const SizedBox(height: AppTheme.spacingMedium),
              const Text('Description:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(report.description),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteReport(report);
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _editReport(report);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToCreateReport([String? category]) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateReportScreen(
          existingReport: category != null ? _createTemplateReport(category) : null,
        ),
      ),
    );

    if (result == true) {
      // Refresh reports list
      await _loadReports();
    }
  }

  Future<void> _createQuickReport(String category) async {
    await _navigateToCreateReport(category);
  }

  ReportModel _createTemplateReport(String category) {
    final now = DateTime.now();
    return ReportModel(
      userId: _currentUser?.id ?? 0,
      title: '${ReportCategory.getDisplayName(category)} Report',
      description: '',
      category: category,
      priority: ReportPriority.medium,
      status: ReportStatus.pending,
      reportDate: now,
      createdAt: now,
      updatedAt: now,
    );
  }

  Future<void> _editReport(ReportModel report) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateReportScreen(
          existingReport: report,
        ),
      ),
    );

    if (result == true) {
      // Refresh reports list
      await _loadReports();
    }
  }

  Future<void> _deleteReport(ReportModel report) async {
    final shouldDelete = await AppUtils.showConfirmationDialog(
      context,
      title: 'Delete Report',
      message: 'Are you sure you want to delete this report? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (shouldDelete && mounted) {
      try {
        await _databaseHelper.deleteReport(report.id!);
        await _loadReports();

        AppUtils.showSnackBar(
          context,
          'Report deleted successfully',
        );
      } catch (e) {
        AppUtils.showSnackBar(
          context,
          'Failed to delete report',
          isError: true,
        );
      }
    }
  }

  void _showInlineCreateForm() {
    setState(() {
      _showCreateForm = true;
      _clearForm();
    });
  }

  void _hideInlineCreateForm() {
    setState(() {
      _showCreateForm = false;
      _clearForm();
    });
  }

  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _locationController.clear();
    _newReportCategory = ReportCategory.weather;
    _newReportPriority = ReportPriority.medium;
    _newReportStatus = ReportStatus.pending;
    _newReportDate = DateTime.now();
  }

  Widget _buildInlineCreateForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with back button
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Row(
                children: [
                  IconButton(
                    onPressed: _hideInlineCreateForm,
                    icon: const Icon(Icons.arrow_back),
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Text(
                    'Create New Report',
                    style: AppTheme.headingMedium,
                  ),
                  const Spacer(),
                  if (_isCreatingReport)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // Basic Information
          _buildInlineBasicInfo(),

          const SizedBox(height: AppTheme.spacingMedium),

          // Category and Priority
          _buildInlineCategoryPriority(),

          const SizedBox(height: AppTheme.spacingMedium),

          // Location and Date
          _buildInlineLocationDate(),

          const SizedBox(height: AppTheme.spacingLarge),

          // Action Buttons
          _buildInlineActionButtons(),
        ],
      ),
    );
  }

  Widget _buildInlineBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Report Details',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title *',
                hintText: 'Enter report title',
                prefixIcon: Icon(Icons.title),
                border: OutlineInputBorder(),
              ),
              maxLength: 100,
              validator: (value) => AppUtils.validateRequired(value, 'Title'),
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description *',
                hintText: 'Describe the issue or feedback',
                prefixIcon: Icon(Icons.description),
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              maxLength: AppConstants.maxReportLength,
              validator: (value) => AppUtils.validateRequired(value, 'Description'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInlineCategoryPriority() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category & Priority',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Category Selection
            DropdownButtonFormField<String>(
              value: _newReportCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                prefixIcon: Icon(Icons.category),
                border: OutlineInputBorder(),
              ),
              items: ReportCategory.all.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(ReportCategory.getDisplayName(category)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _newReportCategory = value!;
                });
              },
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // Priority Selection
            DropdownButtonFormField<String>(
              value: _newReportPriority,
              decoration: const InputDecoration(
                labelText: 'Priority',
                prefixIcon: Icon(Icons.priority_high),
                border: OutlineInputBorder(),
              ),
              items: ReportPriority.all.map((priority) {
                return DropdownMenuItem(
                  value: priority,
                  child: Text(ReportPriority.getDisplayName(priority)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _newReportPriority = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInlineLocationDate() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location & Date',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Location Field
            TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'Location (Optional)',
                hintText: 'Enter location',
                prefixIcon: Icon(Icons.location_on),
                border: OutlineInputBorder(),
              ),
              maxLength: 200,
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // Date Selection
            InkWell(
              onTap: _selectInlineDate,
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.date_range, color: AppTheme.primaryColor),
                    const SizedBox(width: AppTheme.spacingSmall),
                    Text('Date: ${AppUtils.formatDate(_newReportDate)}'),
                    const Spacer(),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInlineActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _isCreatingReport ? null : _hideInlineCreateForm,
            icon: const Icon(Icons.cancel),
            label: const Text('Cancel'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
            ),
          ),
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: _isCreatingReport ? null : _saveInlineReport,
            icon: _isCreatingReport
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            label: Text(_isCreatingReport ? 'Creating...' : 'Create Report'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectInlineDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _newReportDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _newReportDate) {
      setState(() {
        _newReportDate = picked;
      });
    }
  }

  Future<void> _saveInlineReport() async {
    if (!_formKey.currentState!.validate() || _currentUser == null) return;

    setState(() {
      _isCreatingReport = true;
    });

    try {
      final now = DateTime.now();

      final newReport = ReportModel(
        userId: _currentUser!.id!,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _newReportCategory,
        priority: _newReportPriority,
        status: _newReportStatus,
        location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
        reportDate: _newReportDate,
        createdAt: now,
        updatedAt: now,
      );

      await _databaseHelper.insertReport(newReport);

      AppUtils.showSnackBar(
        context,
        AppConstants.successReportSubmitted,
      );

      // Refresh reports and hide form
      await _loadReports();
      _hideInlineCreateForm();

      // Switch to reports tab to show the new report
      _tabController.animateTo(0);

    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to create report. Please try again.',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isCreatingReport = false;
        });
      }
    }
  }
}
