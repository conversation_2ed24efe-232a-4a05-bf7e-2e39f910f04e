import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_constants.dart';
import '../database/database_helper.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  static AuthService get instance => _instance;
  
  AuthService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// Logout user and clear all session data
  Future<bool> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear all user session data
      await prefs.remove(AppConstants.keyIsLoggedIn);
      await prefs.remove(AppConstants.keyUserId);
      await prefs.remove(AppConstants.keyUserEmail);
      await prefs.remove(AppConstants.keyUserName);
      
      // Optionally clear remember me if user wants to logout completely
      await prefs.remove(AppConstants.keyRememberMe);
      await prefs.remove('saved_email');
      
      // Clear any cached data (optional)
      await _clearCachedData();
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Clear cached application data
  Future<void> _clearCachedData() async {
    try {
      // You can add more cache clearing logic here
      // For example, clear temporary files, cached images, etc.
    } catch (e) {
      // Handle cache clearing errors silently
    }
  }

  /// Check if user is currently logged in
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(AppConstants.keyIsLoggedIn) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get current user ID
  Future<int?> getCurrentUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(AppConstants.keyUserId);
    } catch (e) {
      return null;
    }
  }

  /// Get current user email
  Future<String?> getCurrentUserEmail() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(AppConstants.keyUserEmail);
    } catch (e) {
      return null;
    }
  }

  /// Get current user name
  Future<String?> getCurrentUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(AppConstants.keyUserName);
    } catch (e) {
      return null;
    }
  }

  /// Force logout (for security purposes)
  Future<void> forceLogout() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Clear all data including remember me
    await prefs.clear();
    
    // Close database connections
    await _databaseHelper.closeDatabase();
  }

  /// Logout with confirmation
  Future<bool> logoutWithConfirmation() async {
    // This method can be called from UI with confirmation dialog
    return await logout();
  }

  /// Session timeout logout
  Future<void> sessionTimeoutLogout() async {
    await logout();
    // You can add session timeout specific logic here
  }
}
