import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../widgets/app_drawer.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';

class ContactScreen extends StatefulWidget {
  const ContactScreen({super.key});

  @override
  State<ContactScreen> createState() => _ContactScreenState();
}

class _ContactScreenState extends State<ContactScreen> {
  final _databaseHelper = DatabaseHelper();
  UserModel? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);
      
      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
      }
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Contact'),
      ),
      drawer: _currentUser != null
          ? AppDrawer(
              currentRoute: AppConstants.contactRoute,
              userName: _currentUser!.fullName,
              userEmail: _currentUser!.email,
              userAvatar: _currentUser!.profileImage,
            )
          : null,
      body: ListView(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        children: [
          _buildContactInfoCard(),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildContactFormCard(),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildSocialLinksCard(),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildPrivacyPolicyCard(),
        ],
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.envelope,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Get in Touch',
                  style: AppTheme.headingMedium,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            const Text(
              'We\'d love to hear from you! Whether you have questions, feedback, or need support, don\'t hesitate to reach out.',
              style: AppTheme.bodyMedium,
            ),
            const SizedBox(height: AppTheme.spacingLarge),

            // Email
            _buildContactItem(
              icon: FontAwesomeIcons.envelope,
              title: 'Email',
              subtitle: '<EMAIL>',
              onTap: () {
                // TODO: Open email app
              },
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // Phone
            _buildContactItem(
              icon: FontAwesomeIcons.phone,
              title: 'Phone',
              subtitle: '+****************',
              onTap: () {
                // TODO: Open phone app
              },
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // Address
            _buildContactItem(
              icon: FontAwesomeIcons.locationDot,
              title: 'Address',
              subtitle: '123 Weather Street\nClimate City, CC 12345',
              onTap: () {
                // TODO: Open maps app
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingSmall),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingSmall),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: FaIcon(
                icon,
                color: AppTheme.primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppTheme.textHintColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactFormCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Send us a Message',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Subject',
                hintText: 'What is this about?',
                prefixIcon: Icon(Icons.subject),
              ),
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            TextFormField(
              maxLines: 5,
              decoration: const InputDecoration(
                labelText: 'Message',
                hintText: 'Tell us more...',
                prefixIcon: Icon(Icons.message),
                alignLabelWithHint: true,
              ),
            ),

            const SizedBox(height: AppTheme.spacingLarge),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  // TODO: Send message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Message sent successfully!'),
                    ),
                  );
                },
                icon: const Icon(Icons.send),
                label: const Text('Send Message'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialLinksCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Follow Us',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSocialButton(
                  icon: FontAwesomeIcons.twitter,
                  label: 'Twitter',
                  color: const Color(0xFF1DA1F2),
                  onTap: () {
                    // TODO: Open Twitter
                  },
                ),
                _buildSocialButton(
                  icon: FontAwesomeIcons.facebook,
                  label: 'Facebook',
                  color: const Color(0xFF4267B2),
                  onTap: () {
                    // TODO: Open Facebook
                  },
                ),
                _buildSocialButton(
                  icon: FontAwesomeIcons.instagram,
                  label: 'Instagram',
                  color: const Color(0xFFE4405F),
                  onTap: () {
                    // TODO: Open Instagram
                  },
                ),
                _buildSocialButton(
                  icon: FontAwesomeIcons.github,
                  label: 'GitHub',
                  color: const Color(0xFF333333),
                  onTap: () {
                    // TODO: Open GitHub
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingSmall),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: FaIcon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyPolicyCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.shieldHalved,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Privacy & Legal',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            const Text(
              'We value your privacy and are committed to protecting your personal information.',
              style: AppTheme.bodyMedium,
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pushNamed(AppConstants.privacyPolicyRoute),
                icon: const Icon(Icons.privacy_tip),
                label: const Text('View Privacy Policy'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
