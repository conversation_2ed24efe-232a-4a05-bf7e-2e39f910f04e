import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../utils/app_utils.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../models/report_model.dart';

class CreateReportScreen extends StatefulWidget {
  final ReportModel? existingReport; // For editing existing reports

  const CreateReportScreen({
    super.key,
    this.existingReport,
  });

  @override
  State<CreateReportScreen> createState() => _CreateReportScreenState();
}

class _CreateReportScreenState extends State<CreateReportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _databaseHelper = DatabaseHelper();

  UserModel? _currentUser;
  String _selectedCategory = ReportCategory.weather;
  String _selectedPriority = ReportPriority.medium;
  String _selectedStatus = ReportStatus.pending;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.existingReport != null;
    _loadUserData();
    if (_isEditing) {
      _populateExistingData();
    }
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);
      
      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to load user data',
        isError: true,
      );
    }
  }

  void _populateExistingData() {
    final report = widget.existingReport!;
    _titleController.text = report.title;
    _descriptionController.text = report.description;
    _locationController.text = report.location ?? '';
    _selectedCategory = report.category;
    _selectedPriority = report.priority;
    _selectedStatus = report.status;
    _selectedDate = report.reportDate;
  }

  Future<void> _saveReport() async {
    if (!_formKey.currentState!.validate() || _currentUser == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      
      if (_isEditing) {
        // Update existing report
        final updatedReport = widget.existingReport!.copyWith(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          category: _selectedCategory,
          priority: _selectedPriority,
          status: _selectedStatus,
          location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
          reportDate: _selectedDate,
          updatedAt: now,
        );

        await _databaseHelper.updateReport(updatedReport);
        
        AppUtils.showSnackBar(
          context,
          'Report updated successfully',
        );
      } else {
        // Create new report
        final newReport = ReportModel(
          userId: _currentUser!.id!,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          category: _selectedCategory,
          priority: _selectedPriority,
          status: _selectedStatus,
          location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
          reportDate: _selectedDate,
          createdAt: now,
          updatedAt: now,
        );

        await _databaseHelper.insertReport(newReport);
        
        AppUtils.showSnackBar(
          context,
          AppConstants.successReportSubmitted,
        );
      }

      // Navigate back to reports screen
      Navigator.of(context).pop(true); // Return true to indicate success
      
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to save report. Please try again.',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Report' : 'Create Report'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveReport,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildCategorySection(),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildPrioritySection(),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildStatusSection(),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildLocationSection(),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildDateSection(),
              const SizedBox(height: AppTheme.spacingXLarge),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.fileLines,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Report Information',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            
            // Title Field
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Report Title *',
                hintText: 'Enter a descriptive title',
                prefixIcon: Icon(Icons.title),
              ),
              maxLength: 100,
              validator: (value) => AppUtils.validateRequired(value, 'Title'),
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            // Description Field
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description *',
                hintText: 'Provide detailed information about the report',
                prefixIcon: Icon(Icons.description),
                alignLabelWithHint: true,
              ),
              maxLines: 5,
              maxLength: AppConstants.maxReportLength,
              validator: (value) => AppUtils.validateRequired(value, 'Description'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.tags,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Category',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            
            Wrap(
              spacing: AppTheme.spacingSmall,
              children: ReportCategory.all.map((category) {
                final isSelected = _selectedCategory == category;
                return FilterChip(
                  label: Text(ReportCategory.getDisplayName(category)),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  backgroundColor: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : null,
                  selectedColor: AppTheme.primaryColor.withOpacity(0.2),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrioritySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.exclamation,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Priority',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            
            Column(
              children: ReportPriority.all.map((priority) {
                return RadioListTile<String>(
                  title: Text(ReportPriority.getDisplayName(priority)),
                  value: priority,
                  groupValue: _selectedPriority,
                  onChanged: (value) {
                    setState(() {
                      _selectedPriority = value!;
                    });
                  },
                  dense: true,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.listCheck,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Status',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'Report Status',
                prefixIcon: Icon(Icons.flag),
              ),
              items: ReportStatus.all.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(ReportStatus.getDisplayName(status)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.locationDot,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Location (Optional)',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'Location',
                hintText: 'Enter location where the incident occurred',
                prefixIcon: Icon(Icons.place),
              ),
              maxLength: 200,
            ),

            const SizedBox(height: AppTheme.spacingSmall),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _getCurrentLocation,
                    icon: const Icon(Icons.my_location),
                    label: const Text('Use Current Location'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.calendar,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Report Date',
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            InkWell(
              onTap: _selectDate,
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.date_range, color: AppTheme.primaryColor),
                    const SizedBox(width: AppTheme.spacingSmall),
                    Text(
                      AppUtils.formatDate(_selectedDate),
                      style: AppTheme.bodyMedium,
                    ),
                    const Spacer(),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _saveReport,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Icon(_isEditing ? Icons.update : Icons.save),
        label: Text(_isEditing ? 'Update Report' : 'Create Report'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
        ),
      ),
    );
  }

  void _getCurrentLocation() {
    // Placeholder for location functionality
    AppUtils.showSnackBar(
      context,
      'Location feature will be implemented with GPS permissions',
    );

    // For now, set a sample location
    _locationController.text = 'Current Location (GPS)';
  }
}
