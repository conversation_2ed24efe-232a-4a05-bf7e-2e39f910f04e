import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/app_constants.dart';
import '../models/weather_model.dart';

class WeatherService {
  static const String _baseUrl = AppConstants.weatherApiBaseUrl;
  static const String _apiKey = AppConstants.weatherApiKey;

  // Check internet connectivity
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }

      // Additional check: try to reach a reliable server
      try {
        final result = await http.get(
          Uri.parse('https://www.google.com'),
        ).timeout(const Duration(seconds: 5));
        return result.statusCode == 200;
      } catch (e) {
        // If Google is not reachable, try OpenWeatherMap directly
        try {
          final result = await http.get(
            Uri.parse('https://api.openweathermap.org'),
          ).timeout(const Duration(seconds: 5));
          return result.statusCode == 200 || result.statusCode == 401; // 401 means API is reachable but needs key
        } catch (e) {
          return false;
        }
      }
    } catch (e) {
      return false;
    }
  }

  // Get current weather by city name with retry mechanism
  Future<WeatherModel?> getCurrentWeather({
    required String cityName,
    String units = 'metric',
    int retryCount = 0,
  }) async {
    try {
      // Check API key
      if (_apiKey == 'YOUR_OPENWEATHER_API_KEY' || _apiKey.isEmpty) {
        throw Exception('Please configure your OpenWeatherMap API key in app_constants.dart');
      }

      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      final url = Uri.parse(
        '$_baseUrl/weather?q=$cityName&appid=$_apiKey&units=$units',
      );

      print('Weather API URL: $url'); // Debug log

      final response = await http.get(url).timeout(
        const Duration(seconds: 15), // Reduced timeout from 30 to 15 seconds
        onTimeout: () {
          throw Exception('Request timed out. Please check your internet connection and try again.');
        },
      );

      print('Weather API Response: ${response.statusCode}'); // Debug log

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return WeatherModel.fromOpenWeatherMap(data);
      } else if (response.statusCode == 401) {
        throw Exception('Invalid API key. Please check your OpenWeatherMap API key.');
      } else if (response.statusCode == 404) {
        throw Exception('City "$cityName" not found. Please try a different city name.');
      } else if (response.statusCode == 429) {
        throw Exception('API rate limit exceeded. Please try again later.');
      } else {
        final errorBody = response.body;
        print('Weather API Error Body: $errorBody'); // Debug log
        throw Exception('Weather service error (${response.statusCode}). Please try again.');
      }
    } catch (e) {
      print('Weather Service Error: $e'); // Debug log

      // Retry logic for timeout and network errors
      if (retryCount < 2 && (e.toString().contains('TimeoutException') ||
          e.toString().contains('timed out') ||
          e.toString().contains('SocketException'))) {
        print('Retrying weather request... Attempt ${retryCount + 1}');
        await Future.delayed(Duration(seconds: retryCount + 1)); // Progressive delay
        return getCurrentWeather(
          cityName: cityName,
          units: units,
          retryCount: retryCount + 1
        );
      }

      if (e.toString().contains('TimeoutException') || e.toString().contains('timed out')) {
        throw Exception('Request timed out after multiple attempts. Please check your internet connection and try again.');
      } else if (e.toString().contains('SocketException')) {
        throw Exception('Network connection error. Please check your internet connection.');
      } else if (e.toString().contains('HandshakeException')) {
        throw Exception('SSL connection error. Please try again.');
      } else if (e.toString().contains('FormatException')) {
        throw Exception('Invalid response from weather service. Please try again.');
      }

      rethrow;
    }
  }

  // Test API key validity with a simple request
  Future<bool> testApiKey() async {
    try {
      final url = Uri.parse(
        '$_baseUrl/weather?q=London&appid=$_apiKey&units=metric',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: 10),
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // Get weather with better error handling
  Future<WeatherModel?> getCurrentWeatherSafe({
    required String cityName,
    String units = 'metric',
  }) async {
    try {
      // First check if API key is valid
      if (_apiKey == 'YOUR_OPENWEATHER_API_KEY' || _apiKey.isEmpty) {
        throw Exception('Please configure your OpenWeatherMap API key in app settings.');
      }

      // Test connectivity with a quick check
      if (!await hasInternetConnection()) {
        throw Exception('No internet connection. Please check your network settings.');
      }

      // Try the main request
      return await getCurrentWeather(cityName: cityName, units: units);
    } catch (e) {
      rethrow;
    }
  }

  // Get current weather by coordinates
  Future<WeatherModel?> getCurrentWeatherByCoordinates({
    required double latitude,
    required double longitude,
    String units = 'metric',
  }) async {
    try {
      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      final url = Uri.parse(
        '$_baseUrl/weather?lat=$latitude&lon=$longitude&appid=$_apiKey&units=$units',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: AppConstants.connectionTimeout),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return WeatherModel.fromOpenWeatherMap(data);
      } else {
        throw Exception('Failed to fetch weather data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weather: $e');
    }
  }

  // Get 5-day weather forecast
  Future<WeatherForecastModel?> getWeatherForecast({
    required String cityName,
    String units = 'metric',
  }) async {
    try {
      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      final url = Uri.parse(
        '$_baseUrl/forecast?q=$cityName&appid=$_apiKey&units=$units',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: 15), // Reduced timeout
        onTimeout: () {
          throw Exception('Forecast request timed out. Please check your internet connection and try again.');
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return WeatherForecastModel.fromOpenWeatherMap(data);
      } else if (response.statusCode == 404) {
        throw Exception('City not found');
      } else {
        throw Exception('Failed to fetch forecast data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching forecast: $e');
    }
  }

  // Get weather forecast by coordinates
  Future<WeatherForecastModel?> getWeatherForecastByCoordinates({
    required double latitude,
    required double longitude,
    String units = 'metric',
  }) async {
    try {
      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      final url = Uri.parse(
        '$_baseUrl/forecast?lat=$latitude&lon=$longitude&appid=$_apiKey&units=$units',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: AppConstants.connectionTimeout),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return WeatherForecastModel.fromOpenWeatherMap(data);
      } else {
        throw Exception('Failed to fetch forecast data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching forecast: $e');
    }
  }

  // Search cities by name
  Future<List<Map<String, dynamic>>> searchCities(String query) async {
    try {
      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      // Using geocoding API to search for cities
      final url = Uri.parse(
        'http://api.openweathermap.org/geo/1.0/direct?q=$query&limit=5&appid=$_apiKey',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: AppConstants.connectionTimeout),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as List;
        return data.map((item) => {
          'name': item['name'],
          'country': item['country'],
          'state': item['state'],
          'lat': item['lat'],
          'lon': item['lon'],
        }).toList();
      } else {
        throw Exception('Failed to search cities: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error searching cities: $e');
    }
  }

  // Get weather alerts (if available in the API response)
  Future<List<Map<String, dynamic>>> getWeatherAlerts({
    required double latitude,
    required double longitude,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      // Using One Call API for alerts (requires different endpoint)
      final url = Uri.parse(
        'https://api.openweathermap.org/data/3.0/onecall?lat=$latitude&lon=$longitude&appid=$_apiKey',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: AppConstants.connectionTimeout),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        final alerts = data['alerts'] as List? ?? [];
        return alerts.map((alert) => {
          'event': alert['event'],
          'description': alert['description'],
          'start': DateTime.fromMillisecondsSinceEpoch(alert['start'] * 1000),
          'end': DateTime.fromMillisecondsSinceEpoch(alert['end'] * 1000),
        }).toList();
      } else {
        return []; // No alerts or API doesn't support alerts
      }
    } catch (e) {
      return []; // Return empty list if alerts can't be fetched
    }
  }

  // Get air quality data
  Future<Map<String, dynamic>?> getAirQuality({
    required double latitude,
    required double longitude,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        throw Exception(AppConstants.errorNoInternet);
      }

      final url = Uri.parse(
        'http://api.openweathermap.org/data/2.5/air_pollution?lat=$latitude&lon=$longitude&appid=$_apiKey',
      );

      final response = await http.get(url).timeout(
        const Duration(seconds: AppConstants.connectionTimeout),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        final list = data['list'] as List;
        if (list.isNotEmpty) {
          final current = list.first;
          return {
            'aqi': current['main']['aqi'], // Air Quality Index
            'co': current['components']['co'], // Carbon monoxide
            'no': current['components']['no'], // Nitric oxide
            'no2': current['components']['no2'], // Nitrogen dioxide
            'o3': current['components']['o3'], // Ozone
            'so2': current['components']['so2'], // Sulphur dioxide
            'pm2_5': current['components']['pm2_5'], // PM2.5
            'pm10': current['components']['pm10'], // PM10
            'nh3': current['components']['nh3'], // Ammonia
          };
        }
      }
      return null;
    } catch (e) {
      return null; // Return null if air quality can't be fetched
    }
  }

  // Get weather history (if needed)
  Future<List<WeatherModel>> getWeatherHistory({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    // Note: Historical weather data requires a paid subscription
    // This is a placeholder implementation
    try {
      // Implementation would go here for historical data
      return [];
    } catch (e) {
      return [];
    }
  }
}
