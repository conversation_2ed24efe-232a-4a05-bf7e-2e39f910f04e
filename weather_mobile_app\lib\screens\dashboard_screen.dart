import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../utils/app_utils.dart';
import '../widgets/app_drawer.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../models/report_model.dart';
import '../services/auth_service.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final _databaseHelper = DatabaseHelper();
  UserModel? _currentUser;
  List<ReportModel> _recentReports = [];
  bool _isLoading = true;
  
  // Dashboard stats
  int _totalReports = 0;
  int _pendingReports = 0;
  int _completedReports = 0;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);
      
      if (userId != null) {
        // Load user data
        _currentUser = await _databaseHelper.getUserById(userId);
        
        // Load reports data
        final allReports = await _databaseHelper.getReportsByUserId(userId);
        _recentReports = allReports.take(5).toList();
        
        // Calculate stats
        _totalReports = allReports.length;
        _pendingReports = allReports.where((r) => r.status == ReportStatus.pending).length;
        _completedReports = allReports.where((r) => r.status == ReportStatus.completed).length;
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to load dashboard data',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadDashboardData();
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _handleLogout();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: AppTheme.errorColor),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      drawer: _currentUser != null
          ? AppDrawer(
              currentRoute: AppConstants.dashboardRoute,
              userName: _currentUser!.fullName,
              userEmail: _currentUser!.email,
              userAvatar: _currentUser!.profileImage,
            )
          : null,
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(),
              
              const SizedBox(height: AppTheme.spacingLarge),
              
              // Stats Cards
              _buildStatsSection(),
              
              const SizedBox(height: AppTheme.spacingLarge),
              
              // Quick Actions
              _buildQuickActionsSection(),
              
              const SizedBox(height: AppTheme.spacingLarge),
              
              // Recent Reports
              _buildRecentReportsSection(),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamed(AppConstants.reportRoute);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppUtils.getGreeting(),
              style: AppTheme.bodyLarge.copyWith(
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _currentUser?.firstName ?? 'User',
              style: AppTheme.headingMedium.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              'Welcome to your weather dashboard',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Total Reports',
            value: _totalReports.toString(),
            icon: FontAwesomeIcons.fileLines,
            color: AppTheme.infoColor,
          ),
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          child: _buildStatCard(
            title: 'Pending',
            value: _pendingReports.toString(),
            icon: FontAwesomeIcons.clock,
            color: AppTheme.warningColor,
          ),
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          child: _buildStatCard(
            title: 'Completed',
            value: _completedReports.toString(),
            icon: FontAwesomeIcons.check,
            color: AppTheme.successColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          children: [
            FaIcon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              value,
              style: AppTheme.headingMedium.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingSmall,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                title: 'Weather',
                subtitle: 'Check current weather',
                icon: FontAwesomeIcons.cloudSun,
                color: AppTheme.sunnyColor,
                onTap: () => Navigator.of(context).pushNamed(AppConstants.mainRoute),
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: _buildActionCard(
                title: 'New Report',
                subtitle: 'Create a new report',
                icon: FontAwesomeIcons.plus,
                color: AppTheme.primaryColor,
                onTap: () => Navigator.of(context).pushNamed(AppConstants.reportRoute),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: FaIcon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                title,
                style: AppTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentReportsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Reports',
              style: AppTheme.headingSmall,
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pushNamed(AppConstants.reportRoute),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        _recentReports.isEmpty
            ? Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingLarge),
                  child: Column(
                    children: [
                      const FaIcon(
                        FontAwesomeIcons.fileLines,
                        size: 48,
                        color: AppTheme.textHintColor,
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),
                      Text(
                        'No reports yet',
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      const Text(
                        'Create your first report to get started',
                        style: AppTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            : ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _recentReports.length,
                itemBuilder: (context, index) {
                  final report = _recentReports[index];
                  return Card(
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Color(int.parse('0xFF${report.statusColor.substring(1)}')),
                        child: FaIcon(
                          FontAwesomeIcons.fileLines,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      title: Text(
                        report.title,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: Text(
                        '${ReportCategory.getDisplayName(report.category)} • ${AppUtils.formatDate(report.createdAt)}',
                        style: AppTheme.bodySmall,
                      ),
                      trailing: Chip(
                        label: Text(
                          ReportStatus.getDisplayName(report.status),
                          style: const TextStyle(fontSize: 12),
                        ),
                        backgroundColor: Color(int.parse('0xFF${report.statusColor.substring(1)}')).withOpacity(0.1),
                      ),
                      onTap: () {
                        // Navigate to report details
                      },
                    ),
                  );
                },
              ),
      ],
    );
  }

  Future<void> _handleLogout() async {
    final shouldLogout = await AppUtils.showConfirmationDialog(
      context,
      title: 'Logout',
      message: 'Are you sure you want to logout?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
    );

    if (shouldLogout && mounted) {
      // Show loading indicator
      AppUtils.showLoadingDialog(context, message: 'Logging out...');

      try {
        // Perform logout using AuthService
        final success = await AuthService.instance.logout();

        if (mounted) {
          AppUtils.hideLoadingDialog(context);

          if (success) {
            // Show success message
            AppUtils.showSnackBar(
              context,
              'Logged out successfully',
            );

            // Navigate to login screen and clear all previous routes
            Navigator.of(context).pushNamedAndRemoveUntil(
              AppConstants.loginRoute,
              (route) => false,
            );
          } else {
            // Show error message
            AppUtils.showSnackBar(
              context,
              'Failed to logout. Please try again.',
              isError: true,
            );
          }
        }
      } catch (e) {
        if (mounted) {
          AppUtils.hideLoadingDialog(context);
          AppUtils.showSnackBar(
            context,
            'An error occurred during logout.',
            isError: true,
          );
        }
      }
    }
  }
}
