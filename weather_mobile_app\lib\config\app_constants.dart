class AppConstants {
  // App Information
  static const String appName = 'Weather Mobile App';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'A comprehensive weather mobile app with authentication, weather data, and communication features.';
  
  // API Configuration
  static const String weatherApiBaseUrl = 'https://api.openweathermap.org/data/2.5';
  static const String weatherApiKey = '7f9614e9d0a4bbc4df2d4df8a326be80'; // Replace with actual API key from https://openweathermap.org/api
  static const String weatherIconBaseUrl = 'https://openweathermap.org/img/wn';
  
  // Database Configuration
  static const String databaseName = 'weather_app.db';
  static const int databaseVersion = 1;
  
  // Table Names
  static const String usersTable = 'users';
  static const String weatherPreferencesTable = 'weather_preferences';
  static const String reportsTable = 'reports';
  static const String notificationsTable = 'notifications';
  
  // Shared Preferences Keys
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyUserId = 'user_id';
  static const String keyUserEmail = 'user_email';
  static const String keyUserName = 'user_name';
  static const String keyRememberMe = 'remember_me';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyLocationPermission = 'location_permission';
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyTemperatureUnit = 'temperature_unit';
  
  // Navigation Routes
  static const String splashRoute = '/splash';
  static const String loginRoute = '/login';
  static const String signUpRoute = '/signup';
  static const String resetPasswordRoute = '/reset-password';
  static const String dashboardRoute = '/dashboard';
  static const String mainRoute = '/main';
  static const String reportRoute = '/report';
  static const String settingsRoute = '/settings';
  static const String accountRoute = '/account';
  static const String contactRoute = '/contact';
  static const String privacyPolicyRoute = '/privacy-policy';
  
  // Drawer Menu Items
  static const List<Map<String, dynamic>> drawerMenuItems = [
    {
      'title': 'Dashboard',
      'icon': 'dashboard',
      'route': dashboardRoute,
    },
    {
      'title': 'Weather',
      'icon': 'weather',
      'route': mainRoute,
    },
    {
      'title': 'Reports',
      'icon': 'report',
      'route': reportRoute,
    },
    {
      'title': 'Settings',
      'icon': 'settings',
      'route': settingsRoute,
    },
    {
      'title': 'Account',
      'icon': 'account',
      'route': accountRoute,
    },
    {
      'title': 'Contact',
      'icon': 'contact',
      'route': contactRoute,
    },
  ];
  
  // Weather Conditions
  static const Map<String, String> weatherConditions = {
    '01d': 'Clear Sky',
    '01n': 'Clear Sky',
    '02d': 'Few Clouds',
    '02n': 'Few Clouds',
    '03d': 'Scattered Clouds',
    '03n': 'Scattered Clouds',
    '04d': 'Broken Clouds',
    '04n': 'Broken Clouds',
    '09d': 'Shower Rain',
    '09n': 'Shower Rain',
    '10d': 'Rain',
    '10n': 'Rain',
    '11d': 'Thunderstorm',
    '11n': 'Thunderstorm',
    '13d': 'Snow',
    '13n': 'Snow',
    '50d': 'Mist',
    '50n': 'Mist',
  };
  
  // Temperature Units
  static const String celsius = 'metric';
  static const String fahrenheit = 'imperial';
  static const String kelvin = 'standard';
  
  // Default Values
  static const String defaultCity = 'London';
  static const String defaultCountry = 'GB';
  static const String defaultTemperatureUnit = celsius;
  static const int defaultRefreshInterval = 300; // 5 minutes in seconds
  
  // Validation Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';
  static const String passwordPattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  
  // Error Messages
  static const String errorNoInternet = 'No internet connection available';
  static const String errorInvalidCredentials = 'Invalid email or password';
  static const String errorUserNotFound = 'User not found';
  static const String errorEmailExists = 'Email already exists';
  static const String errorWeatherDataFetch = 'Failed to fetch weather data';
  static const String errorLocationPermission = 'Location permission denied';
  static const String errorDatabaseOperation = 'Database operation failed';
  static const String errorGeneral = 'Something went wrong. Please try again.';
  
  // Success Messages
  static const String successLogin = 'Login successful';
  static const String successSignUp = 'Account created successfully';
  static const String successPasswordReset = 'Password reset email sent';
  static const String successDataSaved = 'Data saved successfully';
  static const String successReportSubmitted = 'Report submitted successfully';
  
  // Notification Types
  static const String notificationWeatherAlert = 'weather_alert';
  static const String notificationReminder = 'reminder';
  static const String notificationUpdate = 'update';
  
  // Bluetooth Configuration
  static const String bluetoothServiceUuid = '********-0000-1000-8000-00805F9B34FB';
  static const int bluetoothConnectionTimeout = 10; // seconds
  
  // Animation Durations
  static const int splashDuration = 3000; // milliseconds
  static const int animationDuration = 300; // milliseconds
  static const int fadeAnimationDuration = 500; // milliseconds
  
  // Limits
  static const int maxReportLength = 1000;
  static const int maxUsernameLength = 50;
  static const int maxEmailLength = 100;
  static const int maxPasswordLength = 128;
  static const int maxCityNameLength = 100;
  
  // File Paths
  static const String assetsImagesPath = 'assets/images/';
  static const String assetsIconsPath = 'assets/icons/';
  
  // Image Assets
  static const String logoImage = '${assetsImagesPath}logo.png';
  static const String splashImage = '${assetsImagesPath}splash_bg.png';
  static const String weatherBgImage = '${assetsImagesPath}weather_bg.png';
  static const String defaultAvatarImage = '${assetsImagesPath}default_avatar.png';
  
  // Icon Assets
  static const String appIconImage = '${assetsIconsPath}app_icon.png';
  
  // Network Timeouts
  static const int connectionTimeout = 30; // seconds
  static const int receiveTimeout = 30; // seconds
  static const int sendTimeout = 30; // seconds
}
