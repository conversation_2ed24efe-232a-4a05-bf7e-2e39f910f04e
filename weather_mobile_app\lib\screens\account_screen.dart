import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_theme.dart';
import '../config/app_constants.dart';
import '../widgets/app_drawer.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../utils/app_utils.dart';
import '../services/auth_service.dart';

class AccountScreen extends StatefulWidget {
  const AccountScreen({super.key});

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen> {
  final _databaseHelper = DatabaseHelper();
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  UserModel? _currentUser;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt(AppConstants.keyUserId);

      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
        if (_currentUser != null) {
          _populateForm();
        }
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to load user data',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _populateForm() {
    if (_currentUser != null) {
      _firstNameController.text = _currentUser!.firstName;
      _lastNameController.text = _currentUser!.lastName;
      _emailController.text = _currentUser!.email;
      _phoneController.text = _currentUser!.phone ?? '';
    }
  }

  Future<void> _saveUserData() async {
    if (!_formKey.currentState!.validate() || _currentUser == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final updatedUser = _currentUser!.copyWith(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        updatedAt: DateTime.now(),
      );

      await _databaseHelper.updateUser(updatedUser);

      // Update shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keyUserEmail, updatedUser.email);
      await prefs.setString(AppConstants.keyUserName, updatedUser.fullName);

      setState(() {
        _currentUser = updatedUser;
        _isEditing = false;
      });

      AppUtils.showSnackBar(
        context,
        AppConstants.successDataSaved,
      );
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to update profile',
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _cancelEditing() {
    setState(() {
      _isEditing = false;
    });
    _populateForm(); // Reset form to original values
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Account'),
        actions: [
          if (_isEditing) ...[
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _cancelEditing,
            ),
            IconButton(
              icon: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              onPressed: _isSaving ? null : _saveUserData,
            ),
          ] else
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      drawer: _currentUser != null
          ? AppDrawer(
              currentRoute: AppConstants.accountRoute,
              userName: _currentUser!.fullName,
              userEmail: _currentUser!.email,
              userAvatar: _currentUser!.profileImage,
            )
          : null,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          children: [
            _buildProfileHeader(),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildProfileForm(),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildAccountActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          children: [
            CircleAvatar(
              radius: 50,
              backgroundColor: AppTheme.primaryColor,
              backgroundImage: _currentUser?.profileImage != null
                  ? NetworkImage(_currentUser!.profileImage!)
                  : null,
              child: _currentUser?.profileImage == null
                  ? Text(
                      _currentUser?.initials ?? 'U',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              _currentUser?.fullName ?? 'User',
              style: AppTheme.headingMedium,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              _currentUser?.email ?? '',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              'Member since ${AppUtils.formatDate(_currentUser?.createdAt ?? DateTime.now())}',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textHintColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Profile Information',
                style: AppTheme.headingSmall,
              ),
              const SizedBox(height: AppTheme.spacingMedium),

              // First Name
              TextFormField(
                controller: _firstNameController,
                enabled: _isEditing,
                decoration: const InputDecoration(
                  labelText: 'First Name',
                  prefixIcon: Icon(Icons.person_outline),
                ),
                validator: (value) => AppUtils.validateRequired(value, 'First name'),
              ),

              const SizedBox(height: AppTheme.spacingMedium),

              // Last Name
              TextFormField(
                controller: _lastNameController,
                enabled: _isEditing,
                decoration: const InputDecoration(
                  labelText: 'Last Name',
                  prefixIcon: Icon(Icons.person_outline),
                ),
                validator: (value) => AppUtils.validateRequired(value, 'Last name'),
              ),

              const SizedBox(height: AppTheme.spacingMedium),

              // Email
              TextFormField(
                controller: _emailController,
                enabled: _isEditing,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email_outlined),
                ),
                validator: AppUtils.validateEmail,
              ),

              const SizedBox(height: AppTheme.spacingMedium),

              // Phone
              TextFormField(
                controller: _phoneController,
                enabled: _isEditing,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'Phone (Optional)',
                  prefixIcon: Icon(Icons.phone_outlined),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Actions',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            ListTile(
              leading: const Icon(Icons.lock_outline, color: AppTheme.primaryColor),
              title: const Text('Change Password'),
              subtitle: const Text('Update your account password'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showChangePasswordDialog,
            ),

            const Divider(),

            ListTile(
              leading: const Icon(Icons.download, color: AppTheme.infoColor),
              title: const Text('Export Data'),
              subtitle: const Text('Download your account data'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _exportUserData,
            ),

            const Divider(),

            ListTile(
              leading: const Icon(Icons.delete_outline, color: AppTheme.errorColor),
              title: const Text('Delete Account'),
              subtitle: const Text('Permanently delete your account'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showDeleteAccountDialog,
            ),

            const Divider(),

            ListTile(
              leading: const Icon(Icons.logout, color: AppTheme.errorColor),
              title: const Text('Logout'),
              subtitle: const Text('Sign out of your account'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _handleLogout,
            ),
          ],
        ),
      ),
    );
  }

  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Password'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: currentPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Current Password',
                  prefixIcon: Icon(Icons.lock_outline),
                ),
                validator: (value) => AppUtils.validateRequired(value, 'Current password'),
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              TextFormField(
                controller: newPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'New Password',
                  prefixIcon: Icon(Icons.lock_outline),
                ),
                validator: AppUtils.validatePassword,
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              TextFormField(
                controller: confirmPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Confirm New Password',
                  prefixIcon: Icon(Icons.lock_outline),
                ),
                validator: (value) => AppUtils.validateConfirmPassword(
                  value,
                  newPasswordController.text,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                // TODO: Implement password change
                Navigator.of(context).pop();
                AppUtils.showSnackBar(
                  context,
                  'Password changed successfully',
                );
              }
            },
            child: const Text('Change'),
          ),
        ],
      ),
    );
  }

  void _exportUserData() {
    AppUtils.showSnackBar(
      context,
      'Data export feature will be implemented',
    );
  }

  void _showDeleteAccountDialog() {
    AppUtils.showConfirmationDialog(
      context,
      title: 'Delete Account',
      message: 'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    ).then((confirmed) {
      if (confirmed) {
        // TODO: Implement account deletion
        AppUtils.showSnackBar(
          context,
          'Account deletion feature will be implemented',
        );
      }
    });
  }

  Future<void> _handleLogout() async {
    final shouldLogout = await AppUtils.showConfirmationDialog(
      context,
      title: 'Logout',
      message: 'Are you sure you want to logout? This will sign you out of your account.',
      confirmText: 'Logout',
      cancelText: 'Cancel',
    );

    if (shouldLogout && mounted) {
      // Show loading indicator
      AppUtils.showLoadingDialog(context, message: 'Logging out...');

      try {
        // Perform logout using AuthService
        final success = await AuthService.instance.logout();

        if (mounted) {
          AppUtils.hideLoadingDialog(context);

          if (success) {
            // Show success message
            AppUtils.showSnackBar(
              context,
              'Logged out successfully',
            );

            // Navigate to login screen and clear all previous routes
            Navigator.of(context).pushNamedAndRemoveUntil(
              AppConstants.loginRoute,
              (route) => false,
            );
          } else {
            // Show error message
            AppUtils.showSnackBar(
              context,
              'Failed to logout. Please try again.',
              isError: true,
            );
          }
        }
      } catch (e) {
        if (mounted) {
          AppUtils.hideLoadingDialog(context);
          AppUtils.showSnackBar(
            context,
            'An error occurred during logout.',
            isError: true,
          );
        }
      }
    }
  }
}
